'use client';

import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { name: 'Home', href: '/' },
    { name: 'Blog', href: '/blog' },
    { name: 'About Us', href: '/about' }
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      isScrolled 
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-slate-200/50' 
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/">
              <h1 className={`text-2xl font-bold tracking-tight transition-colors duration-300 ${
                isScrolled ? 'text-slate-900' : 'text-white'
              }`}>
                Light<span className="text-blue-500">Quant</span>
              </h1>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => {
              const isActive = location.pathname === link.href;
              return (
                <Link
                  key={link.name}
                  to={link.href}
                  className={`relative font-medium transition-all duration-300 group ${
                    isScrolled 
                      ? isActive 
                        ? 'text-blue-600' 
                        : 'text-slate-700 hover:text-blue-600'
                      : isActive 
                        ? 'text-blue-400' 
                        : 'text-white/90 hover:text-white'
                  }`}
                >
                  {link.name}
                  {isActive && (
                    <div className={`absolute -bottom-1 left-0 right-0 h-0.5 rounded-full transition-colors duration-300 ${
                      isScrolled ? 'bg-blue-600' : 'bg-blue-400'
                    }`}></div>
                  )}
                  <div className={`absolute -bottom-1 left-0 right-0 h-0.5 rounded-full transition-all duration-300 scale-x-0 group-hover:scale-x-100 ${
                    isScrolled ? 'bg-blue-600' : 'bg-blue-400'
                  }`}></div>
                </Link>
              );
            })}
          </nav>

          {/* Login Button & Mobile Menu */}
          <div className="flex items-center gap-4">
            <button className={`hidden md:block px-6 py-2.5 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 ${
              isScrolled
                ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-blue-500/25'
                : 'bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm border border-white/20'
            }`}>
              Login
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${
                isScrolled ? 'text-slate-700 hover:bg-slate-100' : 'text-white hover:bg-white/10'
              }`}
            >
              {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <div className={`md:hidden transition-all duration-300 overflow-hidden ${
          isMobileMenuOpen ? 'max-h-64 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className={`py-4 space-y-4 ${isScrolled ? 'border-t border-slate-200' : 'border-t border-white/20'}`}>
            {navLinks.map((link) => {
              const isActive = location.pathname === link.href;
              return (
                <Link
                  key={link.name}
                  to={link.href}
                  className={`block font-medium transition-colors duration-300 ${
                    isScrolled 
                      ? isActive 
                        ? 'text-blue-600' 
                        : 'text-slate-700'
                      : isActive 
                        ? 'text-blue-400' 
                        : 'text-white/90'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {link.name}
                </Link>
              );
            })}
            <button className={`w-full px-6 py-2.5 rounded-lg font-semibold transition-all duration-300 ${
              isScrolled
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm border border-white/20'
            }`}>
              Login
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;